{"compilerOptions": {"target": "ES2018", "lib": ["ES2018", "DOM"], "allowJs": true, "checkJs": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["js/*"], "@core/*": ["js/core/*"], "@utils/*": ["js/utils/*"], "@components/*": ["js/components/*"], "@features/*": ["js/features/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["js/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "**/*.backup.js"]}