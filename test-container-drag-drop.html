<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Container Drag-Drop Test - TechManWorkflow-JointJS</title>
    <link rel="stylesheet" href="js/lib/jointjs-3.7.7.min.css">
    <style>
        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            width: 100%;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #paper-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            background-color: #f8f9fa;
        }

        /* 容器拖拽高亮效果 */
        .container-drag-highlight {
            stroke: #87ceeb !important;
            stroke-width: 2 !important;
            filter: drop-shadow(0 0 6px rgba(135, 206, 235, 0.3)) !important;
            transition: all 0.2s ease !important;
        }

        /* 确保容器标题文本不受高亮效果影响 */
        .container-drag-highlight text {
            fill: inherit !important;
            stroke: none !important;
            filter: none !important;
        }

        /* 拖拽状态下的光标 */
        .dragging-cursor {
            cursor: grabbing !important;
        }

        /* 测试说明 */
        .test-instructions {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 300px;
            font-size: 14px;
            z-index: 1000;
        }

        .test-instructions h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .test-instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .test-instructions li {
            margin-bottom: 5px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 测试说明 -->
    <div class="test-instructions">
        <h3>容器拖拽测试 (已优化)</h3>
        <ul>
            <li>✅ 拖拽普通节点到容器上方</li>
            <li>✅ 观察容器边框变浅蓝色高亮</li>
            <li>✅ 释放鼠标将节点嵌入容器</li>
            <li>🔧 拖拽容器时嵌套节点一起移动 (已修复)</li>
            <li>🔧 点击容器时嵌套节点保持可见 (已修复)</li>
            <li>✅ 拖拽嵌套节点出容器边界解除嵌套</li>
        </ul>
    </div>

    <!-- 主画布容器 -->
    <div id="paper-container"></div>

    <!-- JointJS依赖库 -->
    <script src="js/lib/jquery-3.7.1.min.js"></script>
    <script src="js/lib/underscore-1.13.6.min.js"></script>
    <script src="js/lib/backbone-1.4.1.min.js"></script>
    <script src="js/lib/jointjs-3.7.7.min.js"></script>

    <!-- 应用模块 -->
    <script src="js/core/constants.js"></script>
    <script src="js/utils/helpers.js"></script>
    <script src="js/core/graph.js"></script>
    <script src="js/features/node-manager.js"></script>

    <script>
        // 简化的测试应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                // 创建工作流应用实例
                const app = new WorkflowApp();

                // 等待应用初始化完成
                setTimeout(() => {
                    // 创建测试节点
                    createTestNodes(app);
                }, 1000);

            } catch (error) {
                console.error('测试应用初始化失败:', error);
            }
        });

        function createTestNodes(app) {
            // 创建容器节点
            const container = new joint.shapes.standard.Rectangle({
                position: { x: 200, y: 150 },
                size: { width: 300, height: 200 },
                attrs: {
                    body: {
                        fill: 'white',
                        stroke: '#CCCCCC',
                        strokeWidth: 1
                    },
                    label: {
                        text: '容器节点',
                        fontSize: 14,
                        fontFamily: 'Arial, sans-serif',
                        fill: '#333'
                    }
                }
            });

            // 标记为容器
            container.isContainer = true;
            container.isResizable = true;

            // 创建普通节点1
            const node1 = new joint.shapes.standard.Rectangle({
                position: { x: 50, y: 100 },
                size: { width: 100, height: 60 },
                attrs: {
                    body: {
                        fill: '#e3f2fd',
                        stroke: '#1976d2',
                        strokeWidth: 2
                    },
                    label: {
                        text: '节点1',
                        fontSize: 12,
                        fontFamily: 'Arial, sans-serif',
                        fill: '#1976d2'
                    }
                }
            });

            // 创建普通节点2
            const node2 = new joint.shapes.standard.Rectangle({
                position: { x: 50, y: 200 },
                size: { width: 100, height: 60 },
                attrs: {
                    body: {
                        fill: '#f3e5f5',
                        stroke: '#7b1fa2',
                        strokeWidth: 2
                    },
                    label: {
                        text: '节点2',
                        fontSize: 12,
                        fontFamily: 'Arial, sans-serif',
                        fill: '#7b1fa2'
                    }
                }
            });

            // 创建普通节点3
            const node3 = new joint.shapes.standard.Rectangle({
                position: { x: 50, y: 300 },
                size: { width: 100, height: 60 },
                attrs: {
                    body: {
                        fill: '#e8f5e8',
                        stroke: '#388e3c',
                        strokeWidth: 2
                    },
                    label: {
                        text: '节点3',
                        fontSize: 12,
                        fontFamily: 'Arial, sans-serif',
                        fill: '#388e3c'
                    }
                }
            });

            // 添加到图形中
            app.graph.addCells([container, node1, node2, node3]);

            console.log('测试节点已创建，可以开始测试拖拽功能');
        }
    </script>
</body>
</html>
