{"name": "techman-workflow-jointjs", "version": "1.0.0", "description": "工作流设计器 - 基于JointJS的模块化架构", "main": "js/main.js", "scripts": {"dev": "http-server -p 3000 -o", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint js/**/*.js", "type-check": "tsc --noEmit"}, "keywords": ["workflow", "jointjs", "typescript", "workflow-designer"], "author": "TechMan Workflow Team", "license": "MIT", "devDependencies": {"@types/backbone": "^1.4.15", "@types/jest": "^29.5.8", "@types/jointjs": "^1.0.5", "@types/jquery": "^3.5.29", "@types/underscore": "^1.11.15", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "http-server": "^14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}, "dependencies": {"jointjs": "^3.7.7"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js", "<rootDir>/tests/**/*.test.ts"], "collectCoverageFrom": ["js/**/*.js", "!js/**/*.backup.js", "!js/main.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/js/$1", "^@core/(.*)$": "<rootDir>/js/core/$1", "^@utils/(.*)$": "<rootDir>/js/utils/$1", "^@components/(.*)$": "<rootDir>/js/components/$1", "^@features/(.*)$": "<rootDir>/js/features/$1"}}, "eslintConfig": {"env": {"browser": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "no-console": "warn"}, "globals": {"joint": "readonly", "CONFIG": "readonly", "NODE_TYPES": "readonly", "PORT_GROUPS": "readonly", "SHORTCUTS": "readonly", "WorkflowAPI": "readonly"}}}