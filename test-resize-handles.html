<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试调整大小句柄</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        #canvas-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            position: relative;
            overflow: hidden;
        }

        #controls {
            margin-bottom: 20px;
        }

        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .resize-handle {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid white;
            z-index: 10001;
            pointer-events: auto;
            transition: transform 0.2s ease;
        }

        .resize-handle:hover {
            transform: scale(1.2);
            background-color: rgba(0, 0, 0, 0.9);
        }

        .resize-handle-nw {
            cursor: nw-resize;
            clip-path: polygon(0% 0%, 100% 0%, 0% 100%);
        }

        .resize-handle-se {
            cursor: se-resize;
            clip-path: polygon(100% 0%, 100% 100%, 0% 100%);
        }
    </style>
</head>
<body>
    <h1>调整大小句柄缩放测试</h1>

    <div id="controls">
        <button onclick="createContainer()">创建容器</button>
        <button onclick="zoomIn()">放大 (+)</button>
        <button onclick="zoomOut()">缩小 (-)</button>
        <button onclick="resetZoom()">重置缩放</button>
        <span id="zoom-level">缩放: 100%</span>
    </div>

    <div id="canvas-container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jointjs/3.6.2/joint.min.js"></script>
    <script>
        let paper, graph, currentContainer, resizeHandles = [];
        let zoomLevel = 1;

        // 初始化画布
        function initCanvas() {
            graph = new joint.dia.Graph();

            paper = new joint.dia.Paper({
                el: document.getElementById('canvas-container'),
                model: graph,
                width: '100%',
                height: '100%',
                gridSize: 10,
                drawGrid: true,
                background: {
                    color: 'rgba(0, 255, 0, 0.3)'
                }
            });

            // 监听缩放和平移事件
            paper.on('scale translate', () => {
                console.log('缩放或平移事件触发');
                updateResizeHandles();
            });
        }

        // 创建容器
        function createContainer() {
            if (currentContainer) {
                currentContainer.remove();
                removeResizeHandles();
            }

            currentContainer = new joint.shapes.standard.Rectangle({
                position: { x: 100, y: 100 },
                size: { width: 200, height: 150 },
                attrs: {
                    body: {
                        fill: 'lightblue',
                        stroke: '#333',
                        strokeWidth: 2
                    },
                    label: {
                        text: '容器节点',
                        fontSize: 16,
                        fill: 'black'
                    }
                }
            });

            graph.addCell(currentContainer);
            createResizeHandles();
        }

        // 创建调整大小句柄
        function createResizeHandles() {
            removeResizeHandles();

            if (!currentContainer) return;

            const position = currentContainer.position();
            const size = currentContainer.size();
            const handleSize = 12;

            // 创建左上角和右下角句柄
            const directions = ['nw', 'se'];

            directions.forEach(direction => {
                const handle = document.createElement('div');
                handle.className = `resize-handle resize-handle-${direction}`;
                handle.style.width = `${handleSize}px`;
                handle.style.height = `${handleSize}px`;

                // 计算句柄位置
                let handleX, handleY;
                if (direction === 'nw') {
                    handleX = position.x;
                    handleY = position.y;
                } else {
                    handleX = position.x + size.width;
                    handleY = position.y + size.height;
                }

                // 转换为屏幕坐标
                const screenPoint = getScreenCoordinates(handleX, handleY);

                // 调整句柄位置，使其中心点对齐容器角点
                const borderWidth = 2;
                const totalVisualSize = handleSize + (borderWidth * 2);
                const finalLeft = screenPoint.x - totalVisualSize / 2;
                const finalTop = screenPoint.y - totalVisualSize / 2;

                handle.style.left = `${finalLeft}px`;
                handle.style.top = `${finalTop}px`;

                console.log(`${direction} 句柄位置: SVG(${handleX}, ${handleY}) -> Screen(${screenPoint.x}, ${screenPoint.y}) -> Final(${finalLeft}, ${finalTop})`);

                document.body.appendChild(handle);
                resizeHandles.push(handle);
            });
        }

        // 获取屏幕坐标
        function getScreenCoordinates(svgX, svgY) {
            try {
                // 强制刷新SVG变换矩阵
                paper.svg.getBoundingClientRect();

                const svgPoint = paper.svg.createSVGPoint();
                svgPoint.x = svgX;
                svgPoint.y = svgY;

                const ctm = paper.svg.getScreenCTM();
                if (!ctm) {
                    throw new Error('无法获取变换矩阵');
                }

                const screenPoint = svgPoint.matrixTransform(ctm);
                return { x: screenPoint.x, y: screenPoint.y };

            } catch (error) {
                console.warn('SVG转换失败，使用手动计算:', error);

                const scale = zoomLevel;
                const translate = paper.translate();
                const paperRect = paper.el.getBoundingClientRect();

                const transformedX = (svgX * scale) + translate.tx;
                const transformedY = (svgY * scale) + translate.ty;

                const screenX = paperRect.left + transformedX;
                const screenY = paperRect.top + transformedY;

                return { x: screenX, y: screenY };
            }
        }

        // 更新调整大小句柄位置
        function updateResizeHandles() {
            if (resizeHandles.length === 0 || !currentContainer) return;

            console.log('更新调整大小句柄位置，当前缩放:', zoomLevel);

            // 使用requestAnimationFrame确保DOM更新完成
            requestAnimationFrame(() => {
                createResizeHandles();
            });
        }

        // 移除调整大小句柄
        function removeResizeHandles() {
            resizeHandles.forEach(handle => {
                if (handle.parentNode) {
                    handle.parentNode.removeChild(handle);
                }
            });
            resizeHandles = [];
        }

        // 缩放功能
        function zoomIn() {
            zoomLevel = Math.min(3, zoomLevel + 0.2);
            paper.scale(zoomLevel);
            updateZoomDisplay();
        }

        function zoomOut() {
            zoomLevel = Math.max(0.2, zoomLevel - 0.2);
            paper.scale(zoomLevel);
            updateZoomDisplay();
        }

        function resetZoom() {
            zoomLevel = 1;
            paper.scale(zoomLevel);
            updateZoomDisplay();
        }

        function updateZoomDisplay() {
            document.getElementById('zoom-level').textContent = `缩放: ${Math.round(zoomLevel * 100)}%`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initCanvas();
            console.log('测试页面已初始化');
        });
    </script>
</body>
</html>
